#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类编码程序
根据四级分类结构生成8位编码
编码规则：
- 一级分类：01000000, 02000000
- 二级分类：01010000, 01020000  
- 三级分类：01010100, 01010200
- 四级分类：01010101, 01010102
"""

import pandas as pd
import numpy as np

def process_classification_data(input_file, output_file):
    """
    处理分类数据并生成编码
    """
    # 读取Excel文件
    df = pd.read_excel(input_file)

    # 前向填充合并单元格的空值
    df = df.ffill()

    # 定义文档级别映射
    doc_level_mapping = {
        '公司党委重大决策部署': 'S3',
        '总体规划': 'S3',
        '银行信贷业务': 'S3',
        '融资担保业务': 'S3',
        '融资租赁业务': 'S3',
        '债券业务': 'S3',
        '资金管理': 'S3',
        '清欠工作': 'S3',
        '预算管理': 'S3',
        '分析管理': 'S3',
        '司库管理': 'S3',
        '统计管理': 'S3',
        '科研管理': 'S3',
        '知识产权管理': 'S3',
        '质量管理': 'S3',
        '品牌管理': 'S3',
        '科协': 'S3',
        '内部审计': 'S4',
        '合同管理': 'S3',
        '诉讼管理': 'S4',
        '重大项目法律论证': 'S3',
        '人力资源规划': 'S3',
        '员工招聘与配置': 'S3',
        '员工培训与开发': 'S3',
        '劳动关系管理': 'S4',
        '员工绩效考核管理': 'S3',
        '薪酬福利管理': 'S4',
        '固定资产投资': 'S3',
        '股权投资': 'S3',
        '资产处置': 'S3',
        '资产评估': 'S3',
        '产权登记': 'S3',
        '分红管理': 'S3',
        '境外企业管理': 'S3',
        '组织建设': 'S4',
        '领导力发展': 'S4',
        '总图管理': 'S3',
        '供应链管理': 'S3'
    }
    
    # 创建结果列表
    results = []

    # 用于存储各级分类的文档级别
    level_doc_levels = {}

    def get_doc_level(name, parent_level=None):
        """
        获取文档级别，优先匹配映射表，其次继承父级，最后默认S2
        """
        if name in doc_level_mapping:
            return doc_level_mapping[name]
        elif parent_level:
            return parent_level
        else:
            return 'S2'

    # 用于跟踪各级分类的计数器
    level1_counter = {}
    level2_counter = {}
    level3_counter = {}
    level4_counter = {}

    # 用于存储各级分类的编码映射
    level1_codes = {}
    level2_codes = {}
    level3_codes = {}
    level4_codes = {}

    # 用于跟踪排序顺序的计数器
    level1_sort_counter = {}
    level2_sort_counter = {}
    level3_sort_counter = {}
    level4_sort_counter = {}
    
    # 处理每一行数据
    for _, row in df.iterrows():
        level1 = row['文档一级分类名称']
        level2 = row['文档二级分类名称'] 
        level3 = row['文档三级分类名称']
        level4 = row['文档四级分类名称']
        
        # 处理一级分类
        if level1 not in level1_codes:
            if level1 not in level1_counter:
                level1_counter[level1] = len(level1_counter) + 1
            code = f"{level1_counter[level1]:02d}000000"
            level1_codes[level1] = code

            # 计算排序顺序
            if str(level1) == "0":
                sort_order = ""  # 名称为"0"的不设置排序
            else:
                level1_sort_counter[level1] = (len(level1_sort_counter) + 1) * 10
                sort_order = level1_sort_counter[level1]

            # 获取文档级别
            doc_level = get_doc_level(level1)
            level_doc_levels[level1] = doc_level

            results.append({
                '名称': level1,
                '编码': code,
                '上级编码': '',
                '排序顺序': sort_order,
                '级别名': '一级',
                '文档级别': doc_level
            })
        
        # 处理二级分类
        level2_key = f"{level1}|{level2}"
        if level2_key not in level2_codes:
            if level2_key not in level2_counter:
                level2_counter[level2_key] = len([k for k in level2_counter.keys() if k.startswith(f"{level1}|")]) + 1
            parent_code = level1_codes[level1]
            code = f"{parent_code[:2]}{level2_counter[level2_key]:02d}0000"
            level2_codes[level2_key] = code

            # 计算排序顺序（在同一个一级分类下）
            if str(level2) == "0":
                sort_order = ""  # 名称为"0"的不设置排序
            else:
                level1_sort_key = level1
                if level1_sort_key not in level2_sort_counter:
                    level2_sort_counter[level1_sort_key] = {}
                # 只计算非"0"的项目数量
                non_zero_count = len([k for k in level2_sort_counter[level1_sort_key].keys() if str(k) != "0"])
                level2_sort_counter[level1_sort_key][level2] = (non_zero_count + 1) * 10
                sort_order = level2_sort_counter[level1_sort_key][level2]

            # 获取文档级别（优先匹配，否则继承父级）
            parent_doc_level = level_doc_levels.get(level1, 'S2')
            doc_level = get_doc_level(level2, parent_doc_level)
            level_doc_levels[level2_key] = doc_level

            results.append({
                '名称': level2,
                '编码': code,
                '上级编码': parent_code,
                '排序顺序': sort_order,
                '级别名': '二级',
                '文档级别': doc_level
            })
        
        # 处理三级分类
        level3_key = f"{level1}|{level2}|{level3}"
        if level3_key not in level3_codes:
            if level3_key not in level3_counter:
                level3_counter[level3_key] = len([k for k in level3_counter.keys() if k.startswith(f"{level1}|{level2}|")]) + 1
            parent_code = level2_codes[level2_key]
            code = f"{parent_code[:4]}{level3_counter[level3_key]:02d}00"
            level3_codes[level3_key] = code

            # 计算排序顺序（在同一个二级分类下）
            if str(level3) == "0":
                sort_order = ""  # 名称为"0"的不设置排序
            else:
                level2_sort_key = f"{level1}|{level2}"
                if level2_sort_key not in level3_sort_counter:
                    level3_sort_counter[level2_sort_key] = {}
                # 只计算非"0"的项目数量
                non_zero_count = len([k for k in level3_sort_counter[level2_sort_key].keys() if str(k) != "0"])
                level3_sort_counter[level2_sort_key][level3] = (non_zero_count + 1) * 10
                sort_order = level3_sort_counter[level2_sort_key][level3]

            # 获取文档级别（优先匹配，否则继承父级）
            parent_doc_level = level_doc_levels.get(f"{level1}|{level2}", 'S2')
            doc_level = get_doc_level(level3, parent_doc_level)
            level_doc_levels[level3_key] = doc_level

            results.append({
                '名称': level3,
                '编码': code,
                '上级编码': parent_code,
                '排序顺序': sort_order,
                '级别名': '三级',
                '文档级别': doc_level
            })
        
        # 处理四级分类（如果存在且不为空）
        if pd.notna(level4) and str(level4).strip():
            level4_key = f"{level1}|{level2}|{level3}|{level4}"
            if level4_key not in level4_codes:
                if level4_key not in level4_counter:
                    level4_counter[level4_key] = len([k for k in level4_counter.keys() if k.startswith(f"{level1}|{level2}|{level3}|")]) + 1
                parent_code = level3_codes[level3_key]
                code = f"{parent_code[:6]}{level4_counter[level4_key]:02d}"
                level4_codes[level4_key] = code

                # 计算排序顺序（在同一个三级分类下）
                if str(level4) == "0":
                    sort_order = ""  # 名称为"0"的不设置排序
                else:
                    level3_sort_key = f"{level1}|{level2}|{level3}"
                    if level3_sort_key not in level4_sort_counter:
                        level4_sort_counter[level3_sort_key] = {}
                    # 只计算非"0"的项目数量
                    non_zero_count = len([k for k in level4_sort_counter[level3_sort_key].keys() if str(k) != "0"])
                    level4_sort_counter[level3_sort_key][level4] = (non_zero_count + 1) * 10
                    sort_order = level4_sort_counter[level3_sort_key][level4]

                # 获取文档级别（优先匹配，否则继承父级）
                parent_doc_level = level_doc_levels.get(f"{level1}|{level2}|{level3}", 'S2')
                doc_level = get_doc_level(level4, parent_doc_level)

                results.append({
                    '名称': level4,
                    '编码': code,
                    '上级编码': parent_code,
                    '排序顺序': sort_order,
                    '级别名': '四级',
                    '文档级别': doc_level
                })
    
    # 去重（保持顺序）
    seen = set()
    unique_results = []
    for item in results:
        key = (item['名称'], item['编码'])
        if key not in seen:
            seen.add(key)
            unique_results.append(item)
    
    # 创建DataFrame
    result_df = pd.DataFrame(unique_results)

    # 确保编码列为字符串格式，并补齐8位
    result_df['编码'] = result_df['编码'].astype(str).str.zfill(8)

    # 处理上级编码列
    result_df['上级编码'] = result_df['上级编码'].apply(lambda x: x.zfill(8) if x else '')

    # 保存到Excel，确保格式正确
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        result_df.to_excel(writer, index=False, sheet_name='分类编码')

        # 设置列宽和格式
        worksheet = writer.sheets['分类编码']
        worksheet.column_dimensions['A'].width = 30  # 名称列
        worksheet.column_dimensions['B'].width = 12  # 编码列
        worksheet.column_dimensions['C'].width = 12  # 上级编码列
        worksheet.column_dimensions['D'].width = 12  # 排序顺序列
        worksheet.column_dimensions['E'].width = 10  # 级别名列
        worksheet.column_dimensions['F'].width = 12  # 文档级别列

        # 设置格式
        for row in range(2, len(result_df) + 2):  # 从第2行开始（跳过标题）
            worksheet[f'B{row}'].number_format = "@"  # 编码列设为文本格式
            worksheet[f'C{row}'].number_format = "@"  # 上级编码列设为文本格式
    
    print(f"处理完成！")
    print(f"输入文件：{input_file}")
    print(f"输出文件：{output_file}")
    print(f"共生成 {len(unique_results)} 条分类记录")
    
    return result_df

if __name__ == "__main__":
    input_file = "新分类.xlsx"
    output_file = "分类编码结果_含文档级别.xlsx"
    
    try:
        result_df = process_classification_data(input_file, output_file)
        print("\n前10条记录预览：")
        print(result_df.head(10).to_string(index=False))
    except Exception as e:
        print(f"处理过程中出现错误：{e}")
        import traceback
        traceback.print_exc()
