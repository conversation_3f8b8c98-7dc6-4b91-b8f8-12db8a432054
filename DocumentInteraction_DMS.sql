/*
 Navicat Premium Dump SQL

 Source Server         : 文档中心
 Source Server Type    : PostgreSQL
 Source Server Version : 140018 (140018)
 Source Host           : *************:5432
 Source Catalog        : elsbiz
 Source Schema         : sl_baosteeluat

 Target Server Type    : PostgreSQL
 Target Server Version : 140018 (140018)
 File Encoding         : 65001

 Date: 18/08/2025 17:57:29
*/


-- ----------------------------
-- Table structure for zdy_DocumentInteraction_DMS
-- ----------------------------
DROP TABLE IF EXISTS "sl_baosteeluat"."zdy_DocumentInteraction_DMS";
CREATE TABLE "sl_baosteeluat"."zdy_DocumentInteraction_DMS" (
  "id" int8 NOT NULL DEFAULT 0,
  "pagecode" int8 NOT NULL DEFAULT 0,
  "creatorId" int8 NOT NULL DEFAULT 0,
  "createTime" timestamp(6) NOT NULL DEFAULT timezone('utc'::text, now()),
  "lastModifyUserId" int8 NOT NULL DEFAULT 0,
  "lastModifyTime" timestamp(6) NOT NULL DEFAULT timezone('utc'::text, now()),
  "creatorRepresentative" text COLLATE "pg_catalog"."default",
  "lastModifyRepresentative" text COLLATE "pg_catalog"."default",
  "flowFlag" int4 NOT NULL DEFAULT 0,
  "workflowIds" int8[] NOT NULL DEFAULT ARRAY[]::bigint[],
  "masterWorkflowId" int8 NOT NULL DEFAULT 0,
  "masterWorkflowStepId" int8 NOT NULL DEFAULT 0,
  "masterWorkflowDefinitionId" int8 NOT NULL DEFAULT 0,
  "documentManagementID_DMS" int8 NOT NULL DEFAULT 0,
  "issuingDepartmentIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "distributeUsersIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "distributionTime_DMS" timestamp(6) NOT NULL DEFAULT '1900-01-01 00:00:00'::timestamp without time zone,
  "receivingTime_DMS" timestamp(6) NOT NULL DEFAULT '1900-01-01 00:00:00'::timestamp without time zone,
  "receivingStatus_DMS" int4 NOT NULL DEFAULT 0,
  "files_DMS" jsonb[],
  "receivierID_DMS" int8 NOT NULL DEFAULT 0,
  "distributionID_DMS" int8 NOT NULL DEFAULT 0,
  "reviewersIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "hxvalue_DMS" text COLLATE "pg_catalog"."default",
  "code_DMS" text COLLATE "pg_catalog"."default",
  "freeText1_DMS" text COLLATE "pg_catalog"."default",
  "freeDatetime1_DMS" timestamp(6) NOT NULL DEFAULT '1900-01-01 00:00:00'::timestamp without time zone,
  "receiveDepId_DMS" int8 NOT NULL DEFAULT 0,
  "documentSecretId_DMS" int8 NOT NULL DEFAULT 0,
  "freeBoolean_DMS" bool NOT NULL DEFAULT false,
  "freeNumber1_DMS" int4 NOT NULL DEFAULT 0,
  "departmentUserID1_DMS_baosteeluat" int8 NOT NULL DEFAULT 0,
  "receiveUseId_DMS_baosteeluat" int8 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."id" IS 'id';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."pagecode" IS 'pagecode';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."creatorId" IS 'creatorId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."createTime" IS 'createTime';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."lastModifyUserId" IS 'lastModifyUserId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."lastModifyTime" IS 'lastModifyTime';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."creatorRepresentative" IS 'creatorRepresentative';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."lastModifyRepresentative" IS 'lastModifyRepresentative';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."flowFlag" IS 'flowFlag';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."workflowIds" IS 'workflowIds';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."masterWorkflowId" IS 'masterWorkflowId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."masterWorkflowStepId" IS 'masterWorkflowStepId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."masterWorkflowDefinitionId" IS 'masterWorkflowDefinitionId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."documentManagementID_DMS" IS '文档编号';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."issuingDepartmentIDs_DMS" IS '发放部门';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."distributeUsersIDs_DMS" IS '发放用户';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."distributionTime_DMS" IS '发放时间';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."receivingTime_DMS" IS '接收时间';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."receivingStatus_DMS" IS '接收状态';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."files_DMS" IS '接收签名';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."receivierID_DMS" IS '接收人';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."distributionID_DMS" IS '发放ID';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."reviewersIDs_DMS" IS '审核人员';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."hxvalue_DMS" IS '哈希值';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."code_DMS" IS '流水号';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."freeText1_DMS" IS '备用字符串';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."freeDatetime1_DMS" IS '备用日期时间';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."receiveDepId_DMS" IS '接收部门id';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."documentSecretId_DMS" IS '秘密id';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."freeBoolean_DMS" IS '是否打印';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."freeNumber1_DMS" IS '借阅类型';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."departmentUserID1_DMS_baosteeluat" IS '部门负责人';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentInteraction_DMS"."receiveUseId_DMS_baosteeluat" IS '接收人员';

-- ----------------------------
-- Indexes structure for table zdy_DocumentInteraction_DMS
-- ----------------------------
CREATE INDEX "zdy_DocumentInteraction_DMS_distributionTime_DMS_pagecode_c_idx" ON "sl_baosteeluat"."zdy_DocumentInteraction_DMS" USING btree (
  "distributionTime_DMS" "pg_catalog"."timestamp_ops" ASC NULLS LAST,
  "pagecode" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "creatorId" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "documentManagementID_DMS" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "zdy_DocumentInteraction_DMS_pagecode_documentManagementID_D_idx" ON "sl_baosteeluat"."zdy_DocumentInteraction_DMS" USING btree (
  "pagecode" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "documentManagementID_DMS" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "receivingStatus_DMS" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "receivierID_DMS" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table zdy_DocumentInteraction_DMS
-- ----------------------------
ALTER TABLE "sl_baosteeluat"."zdy_DocumentInteraction_DMS" ADD CONSTRAINT "zdy_DocumentInteraction_DMS_pkey" PRIMARY KEY ("id");
