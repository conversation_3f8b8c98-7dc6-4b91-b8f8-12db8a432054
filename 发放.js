list=getListJSONObjects(obj,"refDocumentInteraction_DMS");

if(!ListExtensions.isListNullOrEmpty(list)){
    documentIds=ExpressUtils.getListLongFromJSONObjs(list,"documentManagementID_DMS");
    depIds=ExpressUtils.getListLongFromJSONObjs(list,"issuingDepartmentIDs_DMS");

    // 获取接受人员ID列表
    receiveUserIds=ExpressUtils.getListLongFromJSONObjs(list,"receiveUseId_DMS_baosteeluat");

    userIds=new ArrayList();
    /**搜索已接受过的记录，防止重复接收**/
    sql=getReadSqlExpression("DocumentInteraction_DMS",user);
    sql.comparsionAnd("pagecode",ComparsionOperationEnum.EQUAL,1);
    sql.comparsionAnd("documentManagementID_DMS",ComparsionOperationEnum.EXIST,documentIds);
    sql.comparsionAnd("receivingStatus_DMS",ComparsionOperationEnum.NOT_EQUAL,2);
    sql.comparsionAnd("receiveDepId_DMS",ComparsionOperationEnum.EXIST,depIds);
    // 如果有指定接受人员，也要检查是否已经接受过
    if(!ListExtensions.isListNullOrEmpty(receiveUserIds)){
        sql.comparsionAnd("receiveUseId_DMS_baosteeluat",ComparsionOperationEnum.EXIST,receiveUserIds);
    }
    result=getAllObjects("InteractiveView_DMS",sql,user);
    if(!result.isSuccessful()){
        return result;
    }

    existedList=result.getContents();
    

    saveObjs=new ArrayList();
    for(i=0;i<list.size();i++){
        item=list.get(i);
        matechds=ExpressUtils.filterByLong(existedList,"","documentManagementID_DMS",item.getLong("documentManagementID_DMS"),user);

        // 获取当前项目的接受人员ID
        currentReceiveUserId = item.getLong("receiveUseId_DMS_baosteeluat");

        if(item.getJSONArray("issuingDepartmentIDs_DMS")!=null&&item.getJSONArray("issuingDepartmentIDs_DMS").size()>0){
            depIds=getListLongFromJSONObj(item,"issuingDepartmentIDs_DMS");
            needDepIds=new ArrayList();
            receiveDepIds=ExpressUtils.getListLongFromJSONObjs(matechds,"receiveDepId_DMS");

            // 如果指定了接受人员，还需要检查该用户是否已经接受过
            existedReceiveUserIds=ExpressUtils.getListLongFromJSONObjs(matechds,"receiveUseId_DMS_baosteeluat");

            for(j=0;j<depIds.size();j++){
                did=depIds.get(j);
                // 检查部门是否已经接受过
                departmentAlreadyReceived = !ListExtensions.isListNullOrEmpty(receiveDepIds) && receiveDepIds.contains(did);

                // 如果指定了接受人员，还要检查该用户是否已经接受过
                userAlreadyReceived = false;
                if(currentReceiveUserId != null && currentReceiveUserId > 0){
                    userAlreadyReceived = !ListExtensions.isListNullOrEmpty(existedReceiveUserIds) && existedReceiveUserIds.contains(currentReceiveUserId);
                }

                // 如果部门和用户都没有接受过，则添加到需要发放的列表
                if(!departmentAlreadyReceived && !userAlreadyReceived){
                    needDepIds.add(did);
                }
            }

            depIds=needDepIds;

            if(depIds.size()>0){
                for(j=0;j<depIds.size();j++){
                    did=depIds.get(j);
                    tempList=ExpressUtils.filterByLong(saveObjs,"","documentManagementID_DMS",item.getLong("documentManagementID_DMS"),user);
                    tempList=ExpressUtils.filterByLong(tempList,"","receiveDepId_DMS",did,user);

                    // 如果指定了接受人员，还要按接受人员过滤
                    if(currentReceiveUserId != null && currentReceiveUserId > 0){
                        tempList=ExpressUtils.filterByLong(tempList,"","receiveUseId_DMS_baosteeluat",currentReceiveUserId,user);
                    }

                    if(ListExtensions.isListNullOrEmpty(tempList)){
                        temp=new JSONObject();
                        temp.put("pagecode",1);
                        temp.put("receiveDepId_DMS",did);
                        temp.put("documentManagementID_DMS",item.getLong("documentManagementID_DMS"));
                        temp.put("distributionTime_DMS",item.getString("distributionTime_DMS"));
                        temp.put("distributionID_DMS",item.getLong("id"));

                        // 添加接受人员ID
                        if(currentReceiveUserId != null && currentReceiveUserId > 0){
                            temp.put("receiveUseId_DMS_baosteeluat",currentReceiveUserId);
                        }

                        saveObjs.add(temp);
                    }
                }
            }
        }
    }
}

if(!ListExtensions.isListNullOrEmpty(saveObjs)){
    result=NumberGeneratorLogic.applyNumberGenerator("DocumentInteraction_DMS",saveObjs,user);
    if(!result.isSuccessful()){
        return result;    
    }
    
    result=ExpressUtils.addObjects("DocumentInteraction_DMS",saveObjs,user);
    if(!result.isSuccessful()){
        return result;
    }
}

return true;