list=getListJSONObjects(obj,"refDocumentInteraction_DMS");

if(!ListExtensions.isListNullOrEmpty(list)){
    documentIds=ExpressUtils.getListLongFromJSONObjs(list,"documentManagementID_DMS");
    depIds=ExpressUtils.getListLongFromJSONObjs(list,"issuingDepartmentIDs_DMS");
    
    userIds=new ArrayList();
    /**搜索已接受过的记录，防止重复接收**/
    sql=getReadSqlExpression("DocumentInteraction_DMS",user);
    sql.comparsionAnd("pagecode",ComparsionOperationEnum.EQUAL,1);
    sql.comparsionAnd("documentManagementID_DMS",ComparsionOperationEnum.EXIST,documentIds);
    sql.comparsionAnd("receivingStatus_DMS",ComparsionOperationEnum.NOT_EQUAL,2);
    sql.comparsionAnd("receiveDepId_DMS",ComparsionOperationEnum.EXIST,depIds);
    result=getAllObjects("InteractiveView_DMS",sql,user);
    if(!result.isSuccessful()){
        return result;
    }

    existedList=result.getContents();
    

    saveObjs=new ArrayList();
    for(i=0;i<list.size();i++){
        item=list.get(i);
        matechds=ExpressUtils.filterByLong(existedList,"","documentManagementID_DMS",item.getLong("documentManagementID_DMS"),user);

        if(item.getJSONArray("issuingDepartmentIDs_DMS")!=null&&item.getJSONArray("issuingDepartmentIDs_DMS").size()>0){
            depIds=getListLongFromJSONObj(item,"issuingDepartmentIDs_DMS");   
            needDepIds=new ArrayList();
            receiveDepIds=ExpressUtils.getListLongFromJSONObjs(matechds,"receiveDepId_DMS");
   
            for(j=0;j<depIds.size();j++){
                if(!ListExtensions.isListNullOrEmpty(receiveDepIds)&&receiveDepIds.contains(depIds.get(j))){
                }
                else{
                    needDepIds.add(depIds.get(j));
                }
            }
   
            depIds=needDepIds;

            if(depIds.size()>0){
                for(j=0;j<depIds.size();j++){
                    did=depIds.get(j);
                    tempList=ExpressUtils.filterByLong(saveObjs,"","documentManagementID_DMS",item.getLong("documentManagementID_DMS"),user);
                    tempList=ExpressUtils.filterByLong(tempList,"","receiveDepId_DMS",did,user);
                    if(ListExtensions.isListNullOrEmpty(tempList)){
                        temp=new JSONObject();
                        temp.put("pagecode",1);
                        temp.put("receiveDepId_DMS",did);
                        temp.put("documentManagementID_DMS",item.getLong("documentManagementID_DMS"));
                        temp.put("distributionTime_DMS",item.getString("distributionTime_DMS"));
                        temp.put("distributionID_DMS",item.getLong("id"));
                        saveObjs.add(temp);
                    }
                }        
            }
        }
    }
}

if(!ListExtensions.isListNullOrEmpty(saveObjs)){
    result=NumberGeneratorLogic.applyNumberGenerator("DocumentInteraction_DMS",saveObjs,user);
    if(!result.isSuccessful()){
        return result;    
    }
    
    result=ExpressUtils.addObjects("DocumentInteraction_DMS",saveObjs,user);
    if(!result.isSuccessful()){
        return result;
    }
}

return true;