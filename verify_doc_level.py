#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证文档级别设置
"""

import pandas as pd

def verify_doc_levels():
    # 读取结果文件
    df = pd.read_excel('分类编码结果_含文档级别.xlsx', dtype={'编码': str, '上级编码': str})
    
    print('=== 文档级别验证报告 ===')
    print(f'总记录数: {len(df)}')
    print(f'列名: {df.columns.tolist()}')
    
    print('\n=== 文档级别统计 ===')
    print(df['文档级别'].value_counts().sort_index())
    
    print('\n=== 指定级别的分类验证 ===')
    test_names = [
        '公司党委重大决策部署', '总体规划', '银行信贷业务', '融资担保业务',
        '内部审计', '组织建设', '领导力发展', '科研管理', '质量管理',
        '合同管理', '诉讼管理', '人力资源规划', '员工招聘与配置',
        '薪酬福利管理', '劳动关系管理', '固定资产投资', '股权投资'
    ]
    
    for name in test_names:
        matches = df[df['名称'] == name]
        if not matches.empty:
            level = matches.iloc[0]['文档级别']
            print(f'{name}: {level}')
        else:
            print(f'{name}: 未找到')
    
    print('\n=== 继承关系验证 - 公司党委重大决策部署及其子类 ===')
    parent_matches = df[df['名称'] == '公司党委重大决策部署']
    if not parent_matches.empty:
        parent_code = parent_matches.iloc[0]['编码']
        children = df[df['上级编码'] == parent_code]
        print('父级:')
        print(parent_matches[['名称', '编码', '文档级别']].to_string(index=False))
        print('子级:')
        if not children.empty:
            print(children[['名称', '编码', '文档级别']].head().to_string(index=False))
        else:
            print('无子级')
    
    print('\n=== 继承关系验证 - 组织建设及其子类 ===')
    parent_matches = df[df['名称'] == '组织建设']
    if not parent_matches.empty:
        parent_code = parent_matches.iloc[0]['编码']
        children = df[df['上级编码'] == parent_code]
        print('父级:')
        print(parent_matches[['名称', '编码', '文档级别']].to_string(index=False))
        print('子级:')
        if not children.empty:
            print(children[['名称', '编码', '文档级别']].head().to_string(index=False))
        else:
            print('无子级')
    
    print('\n=== 前20条记录示例 ===')
    print(df[['名称', '编码', '级别名', '文档级别']].head(20).to_string(index=False))

if __name__ == "__main__":
    verify_doc_levels()
